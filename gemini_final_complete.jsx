#target photoshop
//
// AllNarrowbandLayers.jsx
//

//
// Generated Fri Sep 05 2025 23:01:45 GMT-0700
//

cTID = function(s) { return app.charIDToTypeID(s); };
sTID = function(s) { return app.stringIDToTypeID(s); };

//
//==================== All Narrowband Layers WITH IMPROVED FILE IMPORT ==============
//
function AllNarrowbandLayers() {

  // AUTOMATED FILE IMPORT WITH SUBSTRING MATCHING
  function importFilesInOrder() {
    var shouldImport = confirm("Do you want to import image files?\n\nYES = Auto-import from folder\nNO = Use existing layers in current document");

    if (!shouldImport) {
      if (!app.documents.length) {
        alert("No document is open and no files will be imported. Please open a document first.");
        return false;
      }
      return true;
    }

    // Select folder containing images
    var sourceFolder = Folder.selectDialog("Select folder containing narrowband images:");
    if (!sourceFolder) {
      alert("No folder selected. Script cancelled.");
      return false;
    }

    // Get all image files from folder
    var imageFiles = sourceFolder.getFiles(/\.(tif|tiff|jpg|jpeg|png|psd|fits|fit)$/i);
    if (imageFiles.length === 0) {
      alert("No image files found in selected folder.");
      return false;
    }

    // Define layer matching patterns (case-insensitive)
    var layerPatterns = [
      {
        name: "Starless_RGB_Combined",
        description: "11. Starless RGB Combined",
        patterns: ["starless_rgb_combined", "starless rgb combined", "starless_rgb", "starless rgb", "starless color", "starless colour"]
      },
      {
        name: "Starless_L_RGB",
        description: "10. Starless L RGB",
        patterns: ["starless_l_rgb", "starless lrgb", "starless l-rgb", "starless l rgb", "lrgb starless"]
      },
      {
        name: "Starless_L",
        description: "9. Starless L",
        patterns: ["starless_l", "starless l ", "starless lum", "starless luminance"]
      },
      {
        name: "Starless_NB_RGB",
        description: "8. Starless NB RGB",
        patterns: ["starless_nb_rgb", "starless nb rgb", "starless nbrgb", "starless nb-rgb", "starless narrowband rgb", "starless duo", "starless tri-band", "starless triband"]
      },
      {
        name: "Starless_HOO_Combined",
        description: "7. Starless HOO Combined",
        patterns: ["starless_hoo_combined", "starless hoo", "hoo starless", "starless-hoo", "starless_nb_hoo"]
      },
      {
        name: "Starless_SII",
        description: "6. Starless SII",
        patterns: ["SII", "sulfur", "sulphur", "S"]
      },
      {
        name: "Starless_OIII",
        description: "5. Starless OIII",
        patterns: ["OIII", "oxygen", "O"]
      },
      {
        name: "Starless_Ha",
        description: "4. Starless Ha",
        patterns: ["Ha", "hydrogen", "H"]
      },
      {
        name: "Stars_HOO_Combined",
        description: "3. Stars HOO Combined",
        patterns: ["stars_hoo_combined", "stars hoo", "hoo stars", "stars-hoo", "stars_hoo", "stars_nb_hoo"]
      },
      {
        name: "Stars_RGB_Combined",
        description: "2. Stars RGB Combined",
        patterns: ["stars_rgb_combined", "stars rgb combined", "stars_rgb", "starsrgb", "stars-rgb", "stars color", "stars colour"]
      },
      {
        name: "Annotation",
        description: "1. Annotation (TOP)",
        patterns: ["annot", "annotation", "note", "label"]
      }
    ];

    // Function to check if a keyword exists as a separate word or bounded by non-alphabetic chars
    function containsKeyword(fileName, keyword) {
      var lowerFileName = fileName.toLowerCase();
      var lowerKeyword = keyword.toLowerCase();

      // Find all occurrences of the keyword
      var index = 0;
      while ((index = lowerFileName.indexOf(lowerKeyword, index)) !== -1) {
        var beforeChar = index === 0 ? '' : lowerFileName.charAt(index - 1);
        var afterChar = index + lowerKeyword.length >= lowerFileName.length ? '' : lowerFileName.charAt(index + lowerKeyword.length);

        // Check if keyword is bounded by non-alphabetic characters
        var beforeOk = beforeChar === '' || !/[a-z]/.test(beforeChar);
        var afterOk = afterChar === '' || !/[a-z]/.test(afterChar);

        if (beforeOk && afterOk) {
          return true;
        }
        index++;
      }
      return false;
    }

    // Function to find matching file for a layer pattern
    function findMatchingFile(layerPattern) {
      for (var i = 0; i < imageFiles.length; i++) {
        var fileName = imageFiles[i].name;

        for (var j = 0; j < layerPattern.patterns.length; j++) {
          var pattern = layerPattern.patterns[j];
          if (containsKeyword(fileName, pattern)) {
            return imageFiles[i];
          }
        }
      }
      return null;
    }

    var doc = null;
    var importedLayers = [];
    var skippedLayers = [];

    // Process each layer pattern in reverse order (bottom to top stacking)
    for (var i = 0; i < layerPatterns.length; i++) {
      var layerPattern = layerPatterns[i];
      var matchingFile = findMatchingFile(layerPattern);

      if (matchingFile) {
        try {
          var sourceDoc = app.open(matchingFile);
          if (sourceDoc.layers.length > 1) {
            sourceDoc.flatten();
          }

          // For first image, create document by duplicating from source (no background layer approach)
          if (!doc) {
            // Create new document by duplicating the source document (avoids Layer 0 entirely)
            doc = sourceDoc.duplicate("Narrowband Processing");

            // Close the source document
            sourceDoc.close(SaveOptions.DONOTSAVECHANGES);

            // Make the new document active
            app.activeDocument = doc;

            // Rename the layer to the correct name
            doc.activeLayer.name = layerPattern.name;

          } else {
            // For subsequent images, duplicate normally
            // Make source document active for duplication
            app.activeDocument = sourceDoc;
            sourceDoc.activeLayer.duplicate(doc);
            sourceDoc.close(SaveOptions.DONOTSAVECHANGES);

            // Switch back to target document and rename the layer
            app.activeDocument = doc;
            doc.activeLayer.name = layerPattern.name;
          }

          // Make Annotation layer truly transparent with alpha channel
          if (layerPattern.name === "Annotation") {
            // Ensure we have alpha channel for transparency
            try {
              // Add alpha channel if it doesn't exist
              if (doc.channels.length < 4) {
                doc.channels.add();
              }

              // Set layer to use transparency (blend mode and opacity)
              doc.activeLayer.opacity = 75; // 75% opacity for visibility
              doc.activeLayer.blendMode = BlendMode.NORMAL; // Normal blend for proper transparency

              // Enable transparency for the layer
              doc.activeLayer.fillOpacity = 75;
            } catch (e) {
              // Fallback to just opacity if alpha channel operations fail
              doc.activeLayer.opacity = 75;
            }
          }

          importedLayers.push(layerPattern.description + " <- " + matchingFile.name);

        } catch (e) {
          skippedLayers.push(layerPattern.description + " (Error: " + e.message + ")");
        }
      } else {
        skippedLayers.push(layerPattern.description + " (No matching file found)");
      }
    }

    // Show import summary
    var summary = "IMPORT SUMMARY:\n\n";
    summary += "IMPORTED (" + importedLayers.length + "):\n";
    for (var i = 0; i < importedLayers.length; i++) {
      summary += "* " + importedLayers[i] + "\n";
    }

    if (skippedLayers.length > 0) {
      summary += "\nSKIPPED (" + skippedLayers.length + "):\n";
      for (var i = 0; i < skippedLayers.length; i++) {
        summary += "* " + skippedLayers[i] + "\n";
      }
      summary += "\nNote: Groups for skipped layers will be automatically excluded from processing.";
    }

    alert(summary);

    return doc !== null; // Return true if at least one file was imported
  }

  // Import files first
  if (!importFilesInOrder()) {
    return;
  }

  // Check which channels are available after import
  var doc = app.activeDocument;
  var hasHydrogen = false;
  var hasOxygen = false;
  var hasSulfur = false;

  try { doc.artLayers.getByName("Starless_Ha"); hasHydrogen = true; } catch (e) {}
  try { doc.artLayers.getByName("Starless_OIII"); hasOxygen = true; } catch (e) {}
  try { doc.artLayers.getByName("Starless_SII"); hasSulfur = true; } catch (e) {}

  // Create narrowband groups for existing channels
  createNarrowbandGroups(hasHydrogen, hasOxygen, hasSulfur);

  alert("SUCCESS! Narrowband processing structure created with imported layers!");

  // Function to create narrowband groups based on available channels
  function createNarrowbandGroups(hasH, hasO, hasS) {
    var doc = app.activeDocument;

    try {
      // Create Hydrogen group if Ha layer exists
      if (hasH) {
        var hydrogenGroup = doc.layerSets.add();
        hydrogenGroup.name = "Hydrogen";
        hydrogenGroup.blendMode = BlendMode.SCREEN;

        // Move Ha layer into group
        try {
          var haLayer = doc.artLayers.getByName("Starless_Ha");
          haLayer.move(hydrogenGroup, ElementPlacement.INSIDE);
        } catch (e) {}
      }

      // Create Oxygen group if OIII layer exists
      if (hasO) {
        var oxygenGroup = doc.layerSets.add();
        oxygenGroup.name = "Oxygen";
        oxygenGroup.blendMode = BlendMode.SCREEN;

        // Move OIII layer into group
        try {
          var oiiiLayer = doc.artLayers.getByName("Starless_OIII");
          oiiiLayer.move(oxygenGroup, ElementPlacement.INSIDE);
        } catch (e) {}
      }

      // Create Sulfur group if SII layer exists
      if (hasS) {
        var sulfurGroup = doc.layerSets.add();
        sulfurGroup.name = "Sulfur";
        sulfurGroup.blendMode = BlendMode.SCREEN;

        // Move SII layer into group
        try {
          var siiLayer = doc.artLayers.getByName("Starless_SII");
          siiLayer.move(sulfurGroup, ElementPlacement.INSIDE);
        } catch (e) {}
      }

      // Create Stars group if star layers exist
      var starsGroup = null;
      try {
        var starsRgbLayer = doc.artLayers.getByName("Stars_RGB_Combined");
        starsGroup = doc.layerSets.add();
        starsGroup.name = "Stars";
        starsGroup.blendMode = BlendMode.SCREEN;
        starsRgbLayer.move(starsGroup, ElementPlacement.INSIDE);
      } catch (e) {}

      try {
        var starsHooLayer = doc.artLayers.getByName("Stars_HOO_Combined");
        if (!starsGroup) {
          starsGroup = doc.layerSets.add();
          starsGroup.name = "Stars";
          starsGroup.blendMode = BlendMode.SCREEN;
        }
        starsHooLayer.move(starsGroup, ElementPlacement.INSIDE);
      } catch (e) {}

      // Create Annotation group if annotation layer exists
      try {
        var annotationLayer = doc.artLayers.getByName("Annotation");
        var annotationGroup = doc.layerSets.add();
        annotationGroup.name = "Annotation";
        annotationGroup.blendMode = BlendMode.NORMAL;
        annotationLayer.move(annotationGroup, ElementPlacement.INSIDE);
      } catch (e) {}

    } catch (e) {
      alert("Error creating narrowband groups: " + e.message);
    }
  }

}

// Run the main function
AllNarrowbandLayers();
